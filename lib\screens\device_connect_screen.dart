import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:esp_blufi/esp_blufi.dart';
import 'package:provider/provider.dart';

import '../providers/device_provider.dart';
import '../constants/constants.dart';
import '../utils/app_logger.dart';
import '../services/wifi_scan_service.dart';
import '../models/wifi.dart';

// WiFi信息类
class WifiInfo {
  final String ssid;
  final int signalStrength;
  final String source; // 'phone' 或 'device'

  WifiInfo({
    required this.ssid,
    required this.signalStrength,
    required this.source,
  });
}

// DeviceConnectScreen 负责显示当前已连接的蓝牙设备列表。
class DeviceConnectScreen extends StatefulWidget {
  @override
  _DeviceConnectScreenState createState() => _DeviceConnectScreenState();
}

class _DeviceConnectScreenState extends State<DeviceConnectScreen> {
  final _formKey = GlobalKey<FormState>();

  //家庭WIFI
  final TextEditingController _accountController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
// 手机热点
  final TextEditingController _accountHostpotController =
      TextEditingController();
  final TextEditingController _passwordHostpotController =
      TextEditingController();

  final _espBlufiPlugin = EspBlufi();

  Map<String, dynamic> scanResult = Map<String, dynamic>();
  bool _isBleConnecting = false;
  bool deviceInit = false;
  bool _isWifiConnected = false;
  bool _isLoading = false;
  bool pwdShow = false;
  String bleMac = '';
  String deviceName = '';

  List<WifiInfo> wifiList = [];
  Map<String, dynamic> deviceData = {};

  @override
  void initState() {
    super.initState();
    // 执行BluFi信息回调
    _espBlufiPlugin.onMessageReceived(
        successCallback: _handleBlufiCallback, errorCallback: (error) {});
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _connectDeviceBle();
    });
  }

  @override
  void dispose() {
    _closeBleConnection(); //退出后关闭通过blufiku的蓝牙连接
    super.dispose();
  }

  //Blufi回调信息处理
  void _handleBlufiCallback(String? data) {
    AppLogger.debug("Blufi回调信息: $data");
    if (mounted) {
      Map<String, dynamic> mapData = jsonDecode(data!);
      if (mapData.containsKey('key')) {
        String key = mapData['key'];
        if (key == 'wifi_info') {
          //设备自身扫描到的WiFi列表
          Map<String, dynamic> peripheral = mapData['value'];
          String ssid = peripheral['ssid'];
          // 从设备扫描的WiFi，信号强度默认设为-50（因为设备扫描不提供信号强度）
          wifiList.add(WifiInfo(
            ssid: ssid,
            signalStrength: -50,
            source: 'device',
          ));
        } else if (key == 'GATT_SUCCESS') {
          //BLE连接成功后，激活设备WiFi并获取WiFi状态
          setState(() {
            _isBleConnecting = false;
            deviceInit = true;
          });
        } else if (key == 'disconnected_device') {
          //BLE连接断开
          setState(() {
            _isBleConnecting = false;
          });
        } else if (key == 'gatt_disconnected') {
          //硬件上设置WiFi连接成功后，里面断开蓝牙，所以在这个回调中处理
          int wifiConnectResult = int.parse(mapData['value']);

          if (wifiConnectResult == 1 && _isWifiConnected == false) {
            //设备WiFi连接成功
            _isWifiConnected = true;
            DeviceProvider deviceProvider =
                Provider.of<DeviceProvider>(context, listen: false);
            //保存热点信息
            wifiConfig.saveConfig(wifiConfig(
              homeSsid: _accountController.text,
              homePassword: _passwordController.text,
              hotspotSsid: _accountHostpotController.text,
              hotspotPassword: _passwordHostpotController.text,
            ));
            setState(() {
              _isLoading = false;
              if (deviceProvider.device == null ||
                  (deviceProvider.device != null &&
                      deviceProvider.device!.deviceName != deviceName)) {
                deviceData['wifiName'] = _accountController.text;
                deviceData['wifiConfig'] = true;
                deviceData['bleMac'] = bleMac;
                deviceData['deviceName'] = deviceName;
                deviceProvider.createDevice(deviceData);
                //设备WiFi连接成功后转跳到设备注册界面,到云端去查询设备是否激活
                Navigator.of(context).pushNamedAndRemoveUntil(
                    Routes.deviceRegistration, (Route<dynamic> route) => false);
              } else if (deviceProvider.device!.deviceName == deviceName) {
                deviceProvider.updateDevice({
                  'wifiName': _accountController.text,
                  'wifiConfig': true,
                });
                // 导航回到主页面并显示设备管理tab（索引2）
                Navigator.of(context)
                    .pushReplacementNamed(Routes.home, arguments: 2);
              }
            });
          }
        } else if (key == 'receive_device_custom_data') {
          deviceName = mapData['value'];
        }
      }
    }
  }

  //连接设备蓝牙
  Future<void> _connectDeviceBle() async {
    bleMac = ModalRoute.of(context)!.settings.arguments as String;
    try {
      setState(() {
        _isBleConnecting = true;
      });

      await _espBlufiPlugin.connectPeripheral(peripheralAddress: bleMac);
    } catch (e, s) {
      AppLogger.error("Error occurred during connectPeripheral: $e",
          error: e, stackTrace: s);
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('设备蓝牙连接失败! 请检查设备。')));
    }
  }

  // 关闭蓝牙连接
  Future<void> _closeBleConnection() async {
    try {
      await _espBlufiPlugin.requestCloseConnection();
    } catch (e) {
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('设备蓝牙断开连接失败!')));
    }
  }

  //设备开启wifi并获取productID
  Future<void> _deviceInit() async {
    //激活设备WiFi并获取WiFi状态
    await _espBlufiPlugin.requestDeviceStatus();
    // await Future.delayed(Duration(seconds: 1));
    // await _espBlufiPlugin.sendCustomData(data: 'get_devicename');
  }

  //获取三元组的设备名称
  Future<void> _getDeviceName() async {
    await _espBlufiPlugin.sendCustomData(data: 'get_devicename');
  }

  //使用手机上的WIFI功能来扫描周围的路由器
  Future<void> _phoneWifiScan() async {
    try {
      final wifiService = WifiScanService();

      // 检查权限
      final hasPermission = await wifiService.hasLocationPermission();
      if (!hasPermission) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('需要位置权限才能扫描WiFi网络')),
        );
        return;
      }

      setState(() {
        if (wifiList.length != 0) {
          wifiList.clear();
        }
        _isLoading = true;
      });

      // 扫描WiFi网络
      final accessPoints = await wifiService.scanWifiNetworks();

      // 将扫描结果添加到wifiList（只添加非空的SSID）
      for (final ap in accessPoints) {
        if (ap.ssid.isNotEmpty &&
            !wifiList.any((wifi) => wifi.ssid == ap.ssid)) {
          wifiList.add(WifiInfo(
            ssid: ap.ssid,
            signalStrength: ap.level,
            source: 'phone',
          ));
        }
      }

      setState(() {
        _isLoading = false;
      });

      // 显示WiFi列表
      _showWifiList();

      AppLogger.info('手机WiFi扫描完成，找到${wifiList.length}个网络');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      AppLogger.error('手机WiFi扫描失败：$e', error: e);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('WiFi扫描失败: $e')),
        );
      }
    }
  }

  //使用设备上的WIFI功能来扫描周围的路由器，也可以使用手机上的WIFI功能来扫描周围的路由器
  // Future<void> _deviceWifiScan() async {
  //   try {
  //     if (_isBleConnecting) {
  //       //蓝牙还在连接中
  //       ScaffoldMessenger.of(context)
  //           .showSnackBar(SnackBar(content: Text('蓝牙还在连接中... 请稍后几秒再重试！')));
  //       return;
  //     }
  //     setState(() {
  //       if (wifiList.length != 0) {
  //         wifiList.clear();
  //       }
  //       _isLoading = true;
  //     });
  //     await _espBlufiPlugin.requestDeviceWifiScan();
  //     await Future.delayed(Duration(seconds: 5));
  //     setState(() {
  //       _isLoading = false;
  //     });
  //     _showWifiList();
  //   } catch (e) {
  //     if (mounted) {
  //       ScaffoldMessenger.of(context)
  //           .showSnackBar(SnackBar(content: Text('设备扫描wifi失败! 请检查设备。')));
  //     }
  //   }
  // }

  // 连接设备wifi
  Future<void> _connectDeviceWifi() async {
    if (_isBleConnecting) {
      //蓝牙还在连接中
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('设备还在连接中... 请稍后几秒再重试！')));
      return;
    }
    // 先验证各个表单字段是否合法
    if ((_formKey.currentState as FormState).validate()) {
      try {
        setState(() {
          _isLoading = true;
        });
        await _espBlufiPlugin.configProvision(
            username: _accountController.text,
            password: _passwordController.text);
      } catch (e, s) {
        // 处理异常，例如打印错误信息或显示错误消息给用户
        AppLogger.error("Error occurred during configProvision: $e",
            error: e, stackTrace: s);
        setState(() {
          _isLoading = false;
        });
        // 可以添加更多的错误处理逻辑，如显示 SnackBar 给用户
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text('配置设备时出错: $e')));
      }
    }
  }

  // 弹窗显示wifi列表
  void _showWifiList() {
    showDialog(
      context: context,
      barrierDismissible: true, // 点击外部可关闭
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('可用WiFi列表'),
          content: Container(
            width: double.maxFinite,
            height: 300,
            child: wifiList.isEmpty
                ? Center(
                    child: Text('未发现可用的WiFi网络'),
                  )
                : ListView.builder(
                    itemCount: wifiList.length,
                    itemBuilder: (context, index) {
                      final wifi = wifiList[index];
                      return ListTile(
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        leading: Icon(
                          _getWifiIcon(wifi.signalStrength),
                          color: _getSignalColor(wifi.signalStrength),
                          size: 24,
                        ),
                        title: Text(
                          wifi.ssid,
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 16,
                          ),
                        ),
                        trailing: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              '${wifi.signalStrength} dBm',
                              style: TextStyle(
                                color: _getSignalColor(wifi.signalStrength),
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              _getSignalStrengthText(wifi.signalStrength),
                              style: TextStyle(
                                color: _getSignalColor(wifi.signalStrength),
                                fontSize: 10,
                              ),
                            ),
                          ],
                        ),
                        onTap: () {
                          _accountController.text = wifi.ssid;
                          Navigator.pop(context); // 关闭弹窗
                        },
                        shape: Border(
                          bottom: BorderSide(
                            color: Colors.grey.shade300,
                            width: 1.0,
                          ),
                        ),
                      );
                    },
                  ),
          ),
        );
      },
    );
  }

  // 根据信号强度获取WiFi图标
  IconData _getWifiIcon(int signalStrength) {
    if (signalStrength >= -50) {
      return Icons.wifi_outlined;
    } else if (signalStrength >= -60) {
      return Icons.wifi_outlined;
    } else if (signalStrength >= -70) {
      return Icons.wifi_2_bar_outlined;
    } else if (signalStrength >= -80) {
      return Icons.wifi_1_bar_outlined;
    } else if (signalStrength >= -90) {
      return Icons.wifi_off_outlined;
    } else {
      return Icons.wifi_off_outlined;
    }
  }

  // 根据信号强度获取颜色
  Color _getSignalColor(int signalStrength) {
    if (signalStrength >= -50) {
      return Colors.green;
    } else if (signalStrength >= -70) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  // 获取信号强度文字描述
  String _getSignalStrengthText(int signalStrength) {
    if (signalStrength >= -50) {
      return '极强';
    } else if (signalStrength >= -60) {
      return '很强';
    } else if (signalStrength >= -70) {
      return '强';
    } else if (signalStrength >= -80) {
      return '中等';
    } else if (signalStrength >= -90) {
      return '弱';
    } else {
      return '极弱';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (deviceInit) {
      _deviceInit();
      _getDeviceName();
      deviceInit = false;
    }
    return Scaffold(
      appBar: AppBar(title: Text('设备WiFi配网')),
      resizeToAvoidBottomInset: true, // 允许页面在键盘弹出时调整大小
      body: Stack(
        fit: StackFit.expand,
        children: [
          SingleChildScrollView(
            // 添加滚动支持
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                // 移除 Center，使用 Column 直接布局
                crossAxisAlignment: CrossAxisAlignment.stretch, // 改为拉伸对齐
                children: [
                  SizedBox(height: 20),
                  TextFormField(
                    autofocus: false,
                    controller: _accountController,
                    decoration: InputDecoration(
                      labelText: '家庭WIFI账号',
                      hintText: 'wifi账号',
                      prefixIcon: Icon(Icons.wifi),
                      suffixIcon: IconButton(
                          icon: Icon(Icons.arrow_forward_ios),
                          onPressed: () async {
                            await _phoneWifiScan();
                          } //扫描WiFi,
                          ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) return '请输入家庭wifi账号';
                      return null;
                    },
                  ),
                  SizedBox(height: 15),
                  TextFormField(
                    autofocus: false,
                    controller: _passwordController,
                    decoration: InputDecoration(
                      labelText: '家庭WIFI密码',
                      hintText: 'wifi密码',
                      prefixIcon: Icon(Icons.lock),
                      suffixIcon: IconButton(
                        icon: Icon(
                            pwdShow ? Icons.visibility : Icons.visibility_off),
                        onPressed: () {
                          setState(() {
                            pwdShow = !pwdShow;
                          });
                        },
                      ),
                    ),
                    obscureText: !pwdShow,
                    validator: (value) {
                      if (value == null || value.isEmpty) return '请输入家庭wifi密码';
                      return null;
                    },
                  ),
                  SizedBox(height: 30),

                  TextFormField(
                    autofocus: false,
                    controller: _accountHostpotController,
                    decoration: InputDecoration(
                      labelText: '手机热点账号',
                      hintText: '热点账号',
                      prefixIcon: Icon(Icons.wifi),
                      // suffixIcon: IconButton(
                      //     icon: Icon(Icons.arrow_forward_ios),
                      //     onPressed: () async {
                      //       await _phoneWifiScan();
                      //     } //扫描WiFi,
                      //     ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) return '请输入手机热点账号';
                      return null;
                    },
                  ),
                  SizedBox(height: 15),
                  TextFormField(
                    autofocus: false,
                    controller: _passwordHostpotController,
                    decoration: InputDecoration(
                      labelText: '手机热点密码',
                      hintText: '热点密码',
                      prefixIcon: Icon(Icons.lock),
                      suffixIcon: IconButton(
                        icon: Icon(
                            pwdShow ? Icons.visibility : Icons.visibility_off),
                        onPressed: () {
                          setState(() {
                            pwdShow = !pwdShow;
                          });
                        },
                      ),
                    ),
                    obscureText: !pwdShow,
                    validator: (value) {
                      if (value == null || value.isEmpty) return '请输入手机热点密码';
                      return null;
                    },
                  ),

                  Padding(
                    padding: const EdgeInsets.only(top: 25),
                    child: ConstrainedBox(
                      constraints: BoxConstraints.expand(height: 55.0),
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orangeAccent, // 设置背景色
                          foregroundColor: Colors.white, // 设置文字颜色
                        ),
                        onPressed: _connectDeviceWifi,
                        child: Text('确定'),
                      ),
                    ),
                  ),
                  // if (_isLoading)  CircularProgressIndicator(),

                  // 添加说明文字
                  Container(
                    margin: const EdgeInsets.only(top: 50),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info_outline,
                                color: Colors.blue, size: 16),
                            SizedBox(width: 6),
                            Text(
                              '网络配置说明',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue.shade700,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 8),
                        Text(
                          '• 家庭WIFI：为项圈在室内时提供网络连接\n• 手机热点：为宠物外出时提供网络连接',
                          style: TextStyle(
                            color: Colors.grey.shade700,
                            fontSize: 13,
                            height: 1.4,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (_isLoading)
            AbsorbPointer(
              absorbing: true,
              child: Container(
                color: Colors.black.withOpacity(0.5), // 半透明背景
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
