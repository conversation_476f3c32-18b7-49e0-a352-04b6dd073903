import 'package:flutter/material.dart';

/// 对话框组件集合
/// 提供各种类型的对话框组件
class DialogWidgets {
  /// 显示警告确认对话框
  /// 
  /// [context] 当前页面的BuildContext
  /// [title] 对话框标题
  /// [content] 对话框内容
  /// [cancelText] 取消按钮文本，默认为"取消"
  /// [confirmText] 确认按钮文本，默认为"确定"
  /// 
  /// 返回值：true表示用户点击确认，false表示用户点击取消或关闭对话框
  static Future<bool> showWarningDialog({
    required BuildContext context,
    required String title,
    required String content,
    String cancelText = '取消',
    String confirmText = '确定',
  }) async {
    if (!context.mounted) return false;

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false, // 点击外部不关闭对话框
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Text(
            content,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop(false);
              },
              child: Text(
                cancelText,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop(true);
              },
              child: Text(
                confirmText,
                style: TextStyle(
                  color: Colors.red[400],
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }

  /// 显示结果通知对话框
  /// 
  /// [context] 当前页面的BuildContext
  /// [title] 对话框标题
  /// [content] 对话框内容
  /// [isSuccess] 是否为成功状态，影响标题颜色
  /// [buttonText] 按钮文本，默认为"确定"
  static Future<void> showResultDialog({
    required BuildContext context,
    required String title,
    required String content,
    bool isSuccess = true,
    String buttonText = '确定',
  }) async {
    if (!context.mounted) return;

    await showDialog(
      context: context,
      barrierDismissible: false, // 点击外部不关闭对话框
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: isSuccess ? Colors.green[600] : Colors.red[600],
            ),
          ),
          content: Text(
            content,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
              child: Text(
                buttonText,
                style: TextStyle(
                  color: isSuccess ? Colors.green[600] : Colors.red[600],
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 显示加载对话框
  /// 
  /// [context] 当前页面的BuildContext
  /// [message] 加载提示信息
  /// 
  /// 返回一个可以用来关闭对话框的函数
  static VoidCallback showLoadingDialog({
    required BuildContext context,
    String message = '处理中...',
  }) {
    if (!context.mounted) return () {};

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Text(
                message,
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        );
      },
    );

    // 返回关闭对话框的函数
    return () {
      if (context.mounted) {
        Navigator.of(context).pop();
      }
    };
  }
}
